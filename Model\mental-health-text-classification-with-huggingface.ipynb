{"metadata": {"kernelspec": {"language": "python", "display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.10.14", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "kaggle": {"accelerator": "nvidiaTeslaT4", "dataSources": [{"sourceId": 10070846, "sourceType": "datasetVersion", "datasetId": 6207281}], "dockerImageVersionId": 30786, "isInternetEnabled": false, "language": "python", "sourceType": "notebook", "isGpuEnabled": true}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "code", "source": "import numpy as np\nimport pandas as pd \nimport matplotlib.pyplot as plt \nimport seaborn as sns \nimport torch\nfrom torch.utils.data import DataLoader\nfrom imblearn.over_sampling import RandomOverSampler\nimport os\n\n\nfrom sklearn.preprocessing import LabelEncoder\nfrom sklearn.model_selection import train_test_split\nfrom sklearn.metrics import confusion_matrix , classification_report\nfrom transformers import Trainer , TrainingArguments , BertTokenizer , BertForSequenceClassification\n\nimport re\nfrom nltk.corpus import stopwords\n\nstopwords = set(stopwords.words(\"english\"))\n\nfrom transformers import AutoModelForSequenceClassification , AutoTokenizer\n\nos.environ[\"WANDB_MODE\"] = \"offline\"\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-05-22T22:34:59.326925Z", "iopub.execute_input": "2025-05-22T22:34:59.327990Z", "iopub.status.idle": "2025-05-22T22:34:59.336682Z", "shell.execute_reply.started": "2025-05-22T22:34:59.327938Z", "shell.execute_reply": "2025-05-22T22:34:59.335341Z"}}, "outputs": [], "execution_count": 9}, {"cell_type": "code", "source": "path = r\"/kaggle/input/sentiment-analysis-for-mental-health/Combined Data.csv\"\n\nif os.path.exists(path) :\n    df = pd.read_csv(path)\n    print(\"Successfully loaded file.\")\n\nelse :\n    print(\"Sorry, your file was not found.\")\n    ", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:16.947733Z", "iopub.execute_input": "2024-12-02T01:37:16.948577Z", "iopub.status.idle": "2024-12-02T01:37:17.335163Z", "shell.execute_reply.started": "2024-12-02T01:37:16.94853Z", "shell.execute_reply": "2024-12-02T01:37:17.334235Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "df.head(10)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:17.336467Z", "iopub.execute_input": "2024-12-02T01:37:17.337108Z", "iopub.status.idle": "2024-12-02T01:37:17.349233Z", "shell.execute_reply.started": "2024-12-02T01:37:17.337067Z", "shell.execute_reply": "2024-12-02T01:37:17.348207Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "df.shape", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:18.374022Z", "iopub.execute_input": "2024-12-02T01:37:18.374381Z", "iopub.status.idle": "2024-12-02T01:37:18.380124Z", "shell.execute_reply.started": "2024-12-02T01:37:18.374351Z", "shell.execute_reply": "2024-12-02T01:37:18.379187Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "df = df.dropna()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:19.696189Z", "iopub.execute_input": "2024-12-02T01:37:19.69699Z", "iopub.status.idle": "2024-12-02T01:37:19.713104Z", "shell.execute_reply.started": "2024-12-02T01:37:19.696951Z", "shell.execute_reply": "2024-12-02T01:37:19.712121Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "df", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:19.858773Z", "iopub.execute_input": "2024-12-02T01:37:19.859243Z", "iopub.status.idle": "2024-12-02T01:37:19.869574Z", "shell.execute_reply.started": "2024-12-02T01:37:19.859215Z", "shell.execute_reply": "2024-12-02T01:37:19.868566Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "df = df.sample(n=6000 , random_state=42).reset_index(drop=True)\ndf", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:20.093555Z", "iopub.execute_input": "2024-12-02T01:37:20.09394Z", "iopub.status.idle": "2024-12-02T01:37:20.108011Z", "shell.execute_reply.started": "2024-12-02T01:37:20.093909Z", "shell.execute_reply": "2024-12-02T01:37:20.107182Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "df.shape", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:20.573893Z", "iopub.execute_input": "2024-12-02T01:37:20.574251Z", "iopub.status.idle": "2024-12-02T01:37:20.579928Z", "shell.execute_reply.started": "2024-12-02T01:37:20.574218Z", "shell.execute_reply": "2024-12-02T01:37:20.578968Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "df = df.drop(columns=[\"Unnamed: 0\"] , axis=1)\ndf", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:20.76536Z", "iopub.execute_input": "2024-12-02T01:37:20.766163Z", "iopub.status.idle": "2024-12-02T01:37:20.775808Z", "shell.execute_reply.started": "2024-12-02T01:37:20.766135Z", "shell.execute_reply": "2024-12-02T01:37:20.77494Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "df.describe(include=\"all\").T", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:20.978448Z", "iopub.execute_input": "2024-12-02T01:37:20.978798Z", "iopub.status.idle": "2024-12-02T01:37:20.999514Z", "shell.execute_reply.started": "2024-12-02T01:37:20.978768Z", "shell.execute_reply": "2024-12-02T01:37:20.998699Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "df.info()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:21.890401Z", "iopub.execute_input": "2024-12-02T01:37:21.891467Z", "iopub.status.idle": "2024-12-02T01:37:21.903565Z", "shell.execute_reply.started": "2024-12-02T01:37:21.891419Z", "shell.execute_reply": "2024-12-02T01:37:21.902488Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "pd.DataFrame(df[\"status\"].value_counts())", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:22.06386Z", "iopub.execute_input": "2024-12-02T01:37:22.064408Z", "iopub.status.idle": "2024-12-02T01:37:22.072834Z", "shell.execute_reply.started": "2024-12-02T01:37:22.064379Z", "shell.execute_reply": "2024-12-02T01:37:22.071899Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "# Preprocessing", "metadata": {}}, {"cell_type": "code", "source": "def preprocessing(text) :\n    \n    text = text.lower()\n\n    text = re.sub(r\"[^\\w\\s]\" , \"\" , text)\n\n    text = re.sub(r\"\\d+\" , \"\" , text)\n\n    words = text.split()\n\n    words = [w for w in words if w not in stopwords]\n\n    preprocessing = \" \".join(words)\n\n    return preprocessing\n\n\n\ndf[\"cleaned_text\"] = df[\"statement\"].apply(preprocessing)\n\ndf[\"statement_length\"] = df[\"statement\"].apply(len)\n\ndf[\"cleaned_text_length\"] = df[\"cleaned_text\"].apply(len)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:22.591906Z", "iopub.execute_input": "2024-12-02T01:37:22.592245Z", "iopub.status.idle": "2024-12-02T01:37:22.837469Z", "shell.execute_reply.started": "2024-12-02T01:37:22.592214Z", "shell.execute_reply": "2024-12-02T01:37:22.836813Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "df.head(10)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:22.893408Z", "iopub.execute_input": "2024-12-02T01:37:22.893724Z", "iopub.status.idle": "2024-12-02T01:37:22.903314Z", "shell.execute_reply.started": "2024-12-02T01:37:22.893697Z", "shell.execute_reply": "2024-12-02T01:37:22.902461Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "df[\"cleaned_text\"][10]", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:23.499536Z", "iopub.execute_input": "2024-12-02T01:37:23.499915Z", "iopub.status.idle": "2024-12-02T01:37:23.506087Z", "shell.execute_reply.started": "2024-12-02T01:37:23.499883Z", "shell.execute_reply": "2024-12-02T01:37:23.505152Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "df['status'].value_counts()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:23.774304Z", "iopub.execute_input": "2024-12-02T01:37:23.774641Z", "iopub.status.idle": "2024-12-02T01:37:23.782129Z", "shell.execute_reply.started": "2024-12-02T01:37:23.774598Z", "shell.execute_reply": "2024-12-02T01:37:23.781198Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "# Split\nX = df.drop(columns=[\"status\"] , axis=1)\ny = df[\"status\"]\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:24.533958Z", "iopub.execute_input": "2024-12-02T01:37:24.534329Z", "iopub.status.idle": "2024-12-02T01:37:24.540454Z", "shell.execute_reply.started": "2024-12-02T01:37:24.534297Z", "shell.execute_reply": "2024-12-02T01:37:24.539496Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "Ra_ov_sam = RandomOverSampler(random_state=42) \nX_train_res, y_train_res = Ra_ov_sam.fit_resample(X, y) \n\ndf = pd.concat([X_train_res, y_train_res] , axis=1)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:25.16535Z", "iopub.execute_input": "2024-12-02T01:37:25.165737Z", "iopub.status.idle": "2024-12-02T01:37:25.197672Z", "shell.execute_reply.started": "2024-12-02T01:37:25.165703Z", "shell.execute_reply": "2024-12-02T01:37:25.196686Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "pd.DataFrame(df['status'].value_counts())", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:25.383471Z", "iopub.execute_input": "2024-12-02T01:37:25.383808Z", "iopub.status.idle": "2024-12-02T01:37:25.393717Z", "shell.execute_reply.started": "2024-12-02T01:37:25.383779Z", "shell.execute_reply": "2024-12-02T01:37:25.392667Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "encode = LabelEncoder()\n\ndf[\"status\"] = encode.fit_transform(df[\"status\"])\n\n\nX = df[\"statement\"]\n\ny = df[\"status\"]", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:25.956036Z", "iopub.execute_input": "2024-12-02T01:37:25.956882Z", "iopub.status.idle": "2024-12-02T01:37:25.963461Z", "shell.execute_reply.started": "2024-12-02T01:37:25.956847Z", "shell.execute_reply": "2024-12-02T01:37:25.962585Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "num_labels = len(df[\"status\"].unique())\nnum_labels", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:26.169157Z", "iopub.execute_input": "2024-12-02T01:37:26.169828Z", "iopub.status.idle": "2024-12-02T01:37:26.175463Z", "shell.execute_reply.started": "2024-12-02T01:37:26.169797Z", "shell.execute_reply": "2024-12-02T01:37:26.17458Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "X_train , X_test , y_train , y_test = train_test_split(X , y , test_size=0.2 , random_state=42)\n\n\nprint(f\"X Train Shape is = {X_train.shape}\")\nprint(f\"X Test Shape is = {X_test.shape}\")\nprint(f\"y Train Shape is = {y_train.shape}\")\nprint(f\"y Test Shape is = {y_test.shape}\")", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:26.423047Z", "iopub.execute_input": "2024-12-02T01:37:26.423377Z", "iopub.status.idle": "2024-12-02T01:37:26.432852Z", "shell.execute_reply.started": "2024-12-02T01:37:26.423348Z", "shell.execute_reply": "2024-12-02T01:37:26.431832Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "y_train.unique()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:26.594704Z", "iopub.execute_input": "2024-12-02T01:37:26.595255Z", "iopub.status.idle": "2024-12-02T01:37:26.600862Z", "shell.execute_reply.started": "2024-12-02T01:37:26.595223Z", "shell.execute_reply": "2024-12-02T01:37:26.600039Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "max([len(c) for c in df[\"statement\"]])", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:26.816402Z", "iopub.execute_input": "2024-12-02T01:37:26.816749Z", "iopub.status.idle": "2024-12-02T01:37:26.825699Z", "shell.execute_reply.started": "2024-12-02T01:37:26.81672Z", "shell.execute_reply": "2024-12-02T01:37:26.824873Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "# Tokenizer ", "metadata": {}}, {"cell_type": "code", "source": "# Tokenization using BERT tokenizer\n\ntok = BertTokenizer.from_pretrained(\"bert-base-uncased\")\n\ntrain_encoding = tok(list(X_train) , padding=True , truncation=True , max_length=200)\ntest_encodeing = tok(list(X_test) , padding=True , truncation=True , max_length=200)\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:27.715911Z", "iopub.execute_input": "2024-12-02T01:37:27.71627Z", "iopub.status.idle": "2024-12-02T01:37:58.453415Z", "shell.execute_reply.started": "2024-12-02T01:37:27.716239Z", "shell.execute_reply": "2024-12-02T01:37:58.452671Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "# Convert to Dataset format\n\nfrom datasets import Dataset\n\ndf_train = Dataset.from_dict({\"input_ids\" : train_encoding[\"input_ids\"] ,\n                              \"attention_mask\" : train_encoding[\"attention_mask\"] , \n                              \"labels\" : y_train.tolist()})\n\n\n\ndf_test = Dataset.from_dict({\"input_ids\" : test_encodeing[\"input_ids\"] , \n                             \"attention_mask\" : test_encodeing[\"attention_mask\"] , \n                             \"labels\" : y_test.tolist()})\n\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:58.455492Z", "iopub.execute_input": "2024-12-02T01:37:58.456156Z", "iopub.status.idle": "2024-12-02T01:37:59.218557Z", "shell.execute_reply.started": "2024-12-02T01:37:58.456114Z", "shell.execute_reply": "2024-12-02T01:37:59.217844Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "# Fine Tuning Bert", "metadata": {}}, {"cell_type": "code", "source": "model = BertForSequenceClassification.from_pretrained(\"bert-base-uncased\" , num_labels=num_labels)\n\n\ntraining_args = TrainingArguments(\n    output_dir=\"./results\",\n    evaluation_strategy=\"epoch\",\n    save_strategy=\"epoch\",\n    learning_rate=2e-5,\n    per_device_train_batch_size=16,\n    per_device_eval_batch_size=16,\n    num_train_epochs=5,\n    weight_decay=0.01,\n    logging_dir=\"./logs\",\n    logging_steps=10,\n    lr_scheduler_type=\"linear\",\n    warmup_steps=500,\n    load_best_model_at_end=True,\n    metric_for_best_model=\"eval_loss\",\n    save_total_limit=3,\n    gradient_accumulation_steps=2 , \n    report_to=\"none\"  # Disables WandB logging\n)\n\n\n# Trainer\ntrainer = Trainer(\n    model=model , \n    args=training_args , \n    train_dataset=df_train , \n    eval_dataset=df_test\n)\n\n\n# Fine-tuning the model\ntrainer.train()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:37:59.219469Z", "iopub.execute_input": "2024-12-02T01:37:59.21975Z", "iopub.status.idle": "2024-12-02T01:59:02.12336Z", "shell.execute_reply.started": "2024-12-02T01:37:59.219725Z", "shell.execute_reply": "2024-12-02T01:59:02.122494Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "#  Model Evaluation\n", "metadata": {}}, {"cell_type": "code", "source": "pred , label , _ = trainer.predict(df_test)\n\npred_labels = np.argmax(pred , axis=1)\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:59:02.125365Z", "iopub.execute_input": "2024-12-02T01:59:02.126132Z", "iopub.status.idle": "2024-12-02T01:59:22.34423Z", "shell.execute_reply.started": "2024-12-02T01:59:02.126084Z", "shell.execute_reply": "2024-12-02T01:59:22.343441Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "cm = confusion_matrix(pred_labels , y_test)\ncm", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:59:24.754112Z", "iopub.execute_input": "2024-12-02T01:59:24.754482Z", "iopub.status.idle": "2024-12-02T01:59:24.763372Z", "shell.execute_reply.started": "2024-12-02T01:59:24.75445Z", "shell.execute_reply": "2024-12-02T01:59:24.762377Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "plt.figure(figsize=(12,8))\nsns.heatmap(cm , annot=True , cbar=True , cmap=\"Blues\" , xticklabels=num_labels , yticklabels=num_labels)\nplt.xlabel('Predicted')\nplt.ylabel('True')\nplt.title('Confusion Matrix')\nplt.show()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:59:25.879079Z", "iopub.execute_input": "2024-12-02T01:59:25.879436Z", "iopub.status.idle": "2024-12-02T01:59:26.13419Z", "shell.execute_reply.started": "2024-12-02T01:59:25.879404Z", "shell.execute_reply": "2024-12-02T01:59:26.133117Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": "print(classification_report(pred_labels , y_test , target_names=encode.classes_))", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:59:26.869511Z", "iopub.execute_input": "2024-12-02T01:59:26.869887Z", "iopub.status.idle": "2024-12-02T01:59:26.88382Z", "shell.execute_reply.started": "2024-12-02T01:59:26.869855Z", "shell.execute_reply": "2024-12-02T01:59:26.882981Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "# Save and Load Model and Tokenizer", "metadata": {}}, {"cell_type": "code", "source": "trainer.save_model(\"saved_mental_status_bert\")\n\ntok.save_pretrained(\"saved_mental_status_bert\")\n\nimport pickle\n\npickle.dump(encode , open(\"encode.pkl\" , \"wb\"))\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-05-22T22:11:45.502438Z", "iopub.execute_input": "2025-05-22T22:11:45.502905Z", "iopub.status.idle": "2025-05-22T22:11:45.800426Z", "shell.execute_reply.started": "2025-05-22T22:11:45.502856Z", "shell.execute_reply": "2025-05-22T22:11:45.798898Z"}}, "outputs": [{"traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mtrainer\u001b[49m\u001b[38;5;241m.\u001b[39msave_model(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msaved_mental_status_bert\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      3\u001b[0m tok\u001b[38;5;241m.\u001b[39msave_pretrained(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msaved_mental_status_bert\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\n", "\u001b[0;31mNameError\u001b[0m: name 'trainer' is not defined"], "ename": "NameError", "evalue": "name 'trainer' is not defined", "output_type": "error"}], "execution_count": 1}, {"cell_type": "code", "source": "model = AutoModelForSequenceClassification.from_pretrained(\"saved_mental_status_bert\")\n\ntok = AutoTokenizer.from_pretrained(\"saved_mental_status_bert\")\n\nencode = pickle.load(open(\"encode.pkl\" , \"rb\"))\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-05-22T22:35:16.253972Z", "iopub.execute_input": "2025-05-22T22:35:16.254462Z", "iopub.status.idle": "2025-05-22T22:36:20.818183Z", "shell.execute_reply.started": "2025-05-22T22:35:16.254416Z", "shell.execute_reply": "2025-05-22T22:36:20.815839Z"}}, "outputs": [{"traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON>r\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/urllib3/connection.py:198\u001b[0m, in \u001b[0;36mHTTPConnection._new_conn\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    197\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 198\u001b[0m     sock \u001b[38;5;241m=\u001b[39m \u001b[43mconnection\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate_connection\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    199\u001b[0m \u001b[43m        \u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_dns_host\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mport\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    200\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    201\u001b[0m \u001b[43m        \u001b[49m\u001b[43msource_address\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msource_address\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    202\u001b[0m \u001b[43m        \u001b[49m\u001b[43msocket_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msocket_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    203\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    204\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m socket\u001b[38;5;241m.\u001b[39mgaierror \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/urllib3/util/connection.py:60\u001b[0m, in \u001b[0;36mcreate_connection\u001b[0;34m(address, timeout, source_address, socket_options)\u001b[0m\n\u001b[1;32m     58\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m LocationParseError(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mhost\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, label empty or too long\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m---> 60\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m res \u001b[38;5;129;01min\u001b[39;00m \u001b[43msocket\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetaddrinfo\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhost\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mport\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfamily\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msocket\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mSOCK_STREAM\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[1;32m     61\u001b[0m     af, socktype, proto, canonname, sa \u001b[38;5;241m=\u001b[39m res\n", "File \u001b[0;32m/opt/conda/lib/python3.10/socket.py:955\u001b[0m, in \u001b[0;36mgetaddrinfo\u001b[0;34m(host, port, family, type, proto, flags)\u001b[0m\n\u001b[1;32m    954\u001b[0m addrlist \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m--> 955\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m res \u001b[38;5;129;01min\u001b[39;00m \u001b[43m_socket\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetaddrinfo\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhost\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mport\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfamily\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mtype\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mproto\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mflags\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[1;32m    956\u001b[0m     af, socktype, proto, canonname, sa \u001b[38;5;241m=\u001b[39m res\n", "\u001b[0;31mgaierror\u001b[0m: [Errno -3] Temporary failure in name resolution", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mNameResolutionError\u001b[0m                       Traceback (most recent call last)", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/urllib3/connectionpool.py:793\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[1;32m    792\u001b[0m \u001b[38;5;66;03m# Make the request on the HTTPConnection object\u001b[39;00m\n\u001b[0;32m--> 793\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    794\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    795\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    796\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    797\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    798\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    799\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    800\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    801\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    802\u001b[0m \u001b[43m    \u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresponse_conn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    803\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    804\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    805\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mresponse_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    806\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    808\u001b[0m \u001b[38;5;66;03m# Everything went great!\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/urllib3/connectionpool.py:491\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[0m\n\u001b[1;32m    490\u001b[0m         new_e \u001b[38;5;241m=\u001b[39m _wrap_proxy_error(new_e, conn\u001b[38;5;241m.\u001b[39mproxy\u001b[38;5;241m.\u001b[39mscheme)\n\u001b[0;32m--> 491\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m new_e\n\u001b[1;32m    493\u001b[0m \u001b[38;5;66;03m# conn.request() calls http.client.*.request, not the method in\u001b[39;00m\n\u001b[1;32m    494\u001b[0m \u001b[38;5;66;03m# urllib3.request. It also calls makefile (recv) on the socket.\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/urllib3/connectionpool.py:467\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[0m\n\u001b[1;32m    466\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 467\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    468\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (SocketTimeout, BaseSSLError) \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/urllib3/connectionpool.py:1099\u001b[0m, in \u001b[0;36mHTTPSConnectionPool._validate_conn\u001b[0;34m(self, conn)\u001b[0m\n\u001b[1;32m   1098\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m conn\u001b[38;5;241m.\u001b[39mis_closed:\n\u001b[0;32m-> 1099\u001b[0m     \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1101\u001b[0m \u001b[38;5;66;03m# TODO revise this, see https://github.com/urllib3/urllib3/issues/2791\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/urllib3/connection.py:616\u001b[0m, in \u001b[0;36mHTTPSConnection.connect\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    615\u001b[0m sock: socket\u001b[38;5;241m.\u001b[39msocket \u001b[38;5;241m|\u001b[39m ssl\u001b[38;5;241m.\u001b[39mSSLSocket\n\u001b[0;32m--> 616\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock \u001b[38;5;241m=\u001b[39m sock \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_new_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    617\u001b[0m server_hostname: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhost\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/urllib3/connection.py:205\u001b[0m, in \u001b[0;36mHTTPConnection._new_conn\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    204\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m socket\u001b[38;5;241m.\u001b[39mgaierror \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m--> 205\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m NameResolutionError(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhost, \u001b[38;5;28mself\u001b[39m, e) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n\u001b[1;32m    206\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m SocketTimeout \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[0;31mNameResolutionError\u001b[0m: <urllib3.connection.HTTPSConnection object at 0x7b6acc6f5870>: Failed to resolve 'huggingface.co' ([Errno -3] Temporary failure in name resolution)", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mMaxRetryError\u001b[0m                             Traceback (most recent call last)", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/requests/adapters.py:667\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    666\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 667\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    668\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    669\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    670\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    671\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    672\u001b[0m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    673\u001b[0m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    674\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    675\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    676\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    677\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    678\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    679\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    681\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/urllib3/connectionpool.py:847\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[1;32m    845\u001b[0m     new_e \u001b[38;5;241m=\u001b[39m ProtocolError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConnection aborted.\u001b[39m\u001b[38;5;124m\"\u001b[39m, new_e)\n\u001b[0;32m--> 847\u001b[0m retries \u001b[38;5;241m=\u001b[39m \u001b[43mretries\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mincrement\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    848\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mnew_e\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_pool\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msys\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexc_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[1;32m    849\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    850\u001b[0m retries\u001b[38;5;241m.\u001b[39msleep()\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/urllib3/util/retry.py:515\u001b[0m, in \u001b[0;36mRetry.increment\u001b[0;34m(self, method, url, response, error, _pool, _stacktrace)\u001b[0m\n\u001b[1;32m    514\u001b[0m     reason \u001b[38;5;241m=\u001b[39m error \u001b[38;5;129;01mor\u001b[39;00m ResponseError(cause)\n\u001b[0;32m--> 515\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m MaxRetryError(_pool, url, reason) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON>son\u001b[39;00m  \u001b[38;5;66;03m# type: ignore[arg-type]\u001b[39;00m\n\u001b[1;32m    517\u001b[0m log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mIncremented Retry for (url=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m): \u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, url, new_retry)\n", "\u001b[0;31mMaxRetryError\u001b[0m: HTTPSConnectionPool(host='huggingface.co', port=443): Max retries exceeded with url: /saved_mental_status_bert/resolve/main/config.json (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x7b6acc6f5870>: Failed to resolve 'huggingface.co' ([Errno -3] Temporary failure in name resolution)\"))", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mConnectionError\u001b[0m                           Traceback (most recent call last)", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/huggingface_hub/file_download.py:1746\u001b[0m, in \u001b[0;36m_get_metadata_or_catch_error\u001b[0;34m(repo_id, filename, repo_type, revision, endpoint, proxies, etag_timeout, headers, token, local_files_only, relative_filename, storage_folder)\u001b[0m\n\u001b[1;32m   1745\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1746\u001b[0m     metadata \u001b[38;5;241m=\u001b[39m \u001b[43mget_hf_file_metadata\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1747\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43metag_timeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtoken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtoken\u001b[49m\n\u001b[1;32m   1748\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1749\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m EntryNotFoundError \u001b[38;5;28;01mas\u001b[39;00m http_error:\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/huggingface_hub/utils/_validators.py:114\u001b[0m, in \u001b[0;36mvalidate_hf_hub_args.<locals>._inner_fn\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    112\u001b[0m     kwargs \u001b[38;5;241m=\u001b[39m smoothly_deprecate_use_auth_token(fn_name\u001b[38;5;241m=\u001b[39mfn\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m, has_token\u001b[38;5;241m=\u001b[39mhas_token, kwargs\u001b[38;5;241m=\u001b[39mkwargs)\n\u001b[0;32m--> 114\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/huggingface_hub/file_download.py:1666\u001b[0m, in \u001b[0;36mget_hf_file_metadata\u001b[0;34m(url, token, proxies, timeout, library_name, library_version, user_agent, headers)\u001b[0m\n\u001b[1;32m   1665\u001b[0m \u001b[38;5;66;03m# Retrieve metadata\u001b[39;00m\n\u001b[0;32m-> 1666\u001b[0m r \u001b[38;5;241m=\u001b[39m \u001b[43m_request_wrapper\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1667\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mHEAD\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1668\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1669\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1670\u001b[0m \u001b[43m    \u001b[49m\u001b[43mallow_redirects\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m   1671\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfollow_relative_redirects\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m   1672\u001b[0m \u001b[43m    \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1673\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1674\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1675\u001b[0m hf_raise_for_status(r)\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/huggingface_hub/file_download.py:364\u001b[0m, in \u001b[0;36m_request_wrapper\u001b[0;34m(method, url, follow_relative_redirects, **params)\u001b[0m\n\u001b[1;32m    363\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m follow_relative_redirects:\n\u001b[0;32m--> 364\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43m_request_wrapper\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    365\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    366\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    367\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfollow_relative_redirects\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01m<PERSON><PERSON><PERSON>\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    368\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    369\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    371\u001b[0m     \u001b[38;5;66;03m# If redirection, we redirect only relative paths.\u001b[39;00m\n\u001b[1;32m    372\u001b[0m     \u001b[38;5;66;03m# This is useful in case of a renamed repository.\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/huggingface_hub/file_download.py:387\u001b[0m, in \u001b[0;36m_request_wrapper\u001b[0;34m(method, url, follow_relative_redirects, **params)\u001b[0m\n\u001b[1;32m    386\u001b[0m \u001b[38;5;66;03m# Perform request and return if status_code is not in the retry list.\u001b[39;00m\n\u001b[0;32m--> 387\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mget_session\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    388\u001b[0m hf_raise_for_status(response)\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/requests/sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[0;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[1;32m    588\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[0;32m--> 589\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprep\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43msend_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    591\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/requests/sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[0;34m(self, request, **kwargs)\u001b[0m\n\u001b[1;32m    702\u001b[0m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[0;32m--> 703\u001b[0m r \u001b[38;5;241m=\u001b[39m \u001b[43madapter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    705\u001b[0m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/huggingface_hub/utils/_http.py:93\u001b[0m, in \u001b[0;36mUniqueRequestIdAdapter.send\u001b[0;34m(self, request, *args, **kwargs)\u001b[0m\n\u001b[1;32m     92\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 93\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     94\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m requests\u001b[38;5;241m.\u001b[39mRequestException \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/requests/adapters.py:700\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    698\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m SSLError(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[0;32m--> 700\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[1;32m    702\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ClosedPoolError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[0;31mConnectionError\u001b[0m: (MaxRetryError('HTTPSConnectionPool(host=\\'huggingface.co\\', port=443): Max retries exceeded with url: /saved_mental_status_bert/resolve/main/config.json (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x7b6acc6f5870>: Failed to resolve \\'huggingface.co\\' ([Errno -3] Temporary failure in name resolution)\"))'), '(Request ID: b476f9b7-8153-42a9-b1f4-812f75be9250)')", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mLocalEntryNotFoundError\u001b[0m                   <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/transformers/utils/hub.py:403\u001b[0m, in \u001b[0;36mcached_file\u001b[0;34m(path_or_repo_id, filename, cache_dir, force_download, resume_download, proxies, token, revision, local_files_only, subfolder, repo_type, user_agent, _raise_exceptions_for_gated_repo, _raise_exceptions_for_missing_entries, _raise_exceptions_for_connection_errors, _commit_hash, **deprecated_kwargs)\u001b[0m\n\u001b[1;32m    401\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    402\u001b[0m     \u001b[38;5;66;03m# Load from URL or cache if already cached\u001b[39;00m\n\u001b[0;32m--> 403\u001b[0m     resolved_file \u001b[38;5;241m=\u001b[39m \u001b[43mhf_hub_download\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    404\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpath_or_repo_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    405\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfilename\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    406\u001b[0m \u001b[43m        \u001b[49m\u001b[43msubfolder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43msubfolder\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m==\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43msubfolder\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    407\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrepo_type\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrepo_type\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    408\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrevision\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    409\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcache_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    410\u001b[0m \u001b[43m        \u001b[49m\u001b[43muser_agent\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muser_agent\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    411\u001b[0m \u001b[43m        \u001b[49m\u001b[43mforce_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    412\u001b[0m \u001b[43m        \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    413\u001b[0m \u001b[43m        \u001b[49m\u001b[43mresume_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresume_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    414\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtoken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    415\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlocal_files_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlocal_files_only\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    416\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    417\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m GatedRepoError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:101\u001b[0m, in \u001b[0;36m_deprecate_arguments.<locals>._inner_deprecate_positional_args.<locals>.inner_f\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    100\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(message, \u001b[38;5;167;01mF<PERSON><PERSON><PERSON>arning\u001b[39;00m)\n\u001b[0;32m--> 101\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/huggingface_hub/utils/_validators.py:114\u001b[0m, in \u001b[0;36mvalidate_hf_hub_args.<locals>._inner_fn\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    112\u001b[0m     kwargs \u001b[38;5;241m=\u001b[39m smoothly_deprecate_use_auth_token(fn_name\u001b[38;5;241m=\u001b[39mfn\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m, has_token\u001b[38;5;241m=\u001b[39mhas_token, kwargs\u001b[38;5;241m=\u001b[39mkwargs)\n\u001b[0;32m--> 114\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/huggingface_hub/file_download.py:1232\u001b[0m, in \u001b[0;36mhf_hub_download\u001b[0;34m(repo_id, filename, subfolder, repo_type, revision, library_name, library_version, cache_dir, local_dir, user_agent, force_download, proxies, etag_timeout, token, local_files_only, headers, endpoint, legacy_cache_layout, resume_download, force_filename, local_dir_use_symlinks)\u001b[0m\n\u001b[1;32m   1231\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1232\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_hf_hub_download_to_cache_dir\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1233\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;66;43;03m# Destination\u001b[39;49;00m\n\u001b[1;32m   1234\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcache_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1235\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;66;43;03m# File info\u001b[39;49;00m\n\u001b[1;32m   1236\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrepo_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrepo_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1237\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfilename\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfilename\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1238\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrepo_type\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrepo_type\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1239\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrevision\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1240\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;66;43;03m# HTTP info\u001b[39;49;00m\n\u001b[1;32m   1241\u001b[0m \u001b[43m        \u001b[49m\u001b[43mendpoint\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mendpoint\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1242\u001b[0m \u001b[43m        \u001b[49m\u001b[43metag_timeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43metag_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1243\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1244\u001b[0m \u001b[43m        \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1245\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtoken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1246\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;66;43;03m# Additional options\u001b[39;49;00m\n\u001b[1;32m   1247\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlocal_files_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlocal_files_only\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1248\u001b[0m \u001b[43m        \u001b[49m\u001b[43mforce_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1249\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/huggingface_hub/file_download.py:1339\u001b[0m, in \u001b[0;36m_hf_hub_download_to_cache_dir\u001b[0;34m(cache_dir, repo_id, filename, repo_type, revision, endpoint, etag_timeout, headers, proxies, token, local_files_only, force_download)\u001b[0m\n\u001b[1;32m   1338\u001b[0m     \u001b[38;5;66;03m# Otherwise, raise appropriate error\u001b[39;00m\n\u001b[0;32m-> 1339\u001b[0m     \u001b[43m_raise_on_head_call_error\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhead_call_error\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mforce_download\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlocal_files_only\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1341\u001b[0m \u001b[38;5;66;03m# From now on, etag, commit_hash, url and size are not None.\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/huggingface_hub/file_download.py:1857\u001b[0m, in \u001b[0;36m_raise_on_head_call_error\u001b[0;34m(head_call_error, force_download, local_files_only)\u001b[0m\n\u001b[1;32m   1855\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1856\u001b[0m     \u001b[38;5;66;03m# Otherwise: most likely a connection issue or Hub downtime => let's warn the user\u001b[39;00m\n\u001b[0;32m-> 1857\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m LocalEntryNotFoundError(\n\u001b[1;32m   1858\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAn error happened while trying to locate the file on the Hub and we cannot find the requested files\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1859\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m in the local cache. Please check your connection and try again or make sure your Internet connection\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1860\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m is on.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1861\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mhead_call_error\u001b[39;00m\n", "\u001b[0;31mLocalEntryNotFoundError\u001b[0m: An error happened while trying to locate the file on the Hub and we cannot find the requested files in the local cache. Please check your connection and try again or make sure your Internet connection is on.", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mOSError\u001b[0m                                   Traceback (most recent call last)", "Cell \u001b[0;32mIn[10], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m model \u001b[38;5;241m=\u001b[39m \u001b[43mAutoModelForSequenceClassification\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_pretrained\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43msaved_mental_status_bert\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m      3\u001b[0m tok \u001b[38;5;241m=\u001b[39m AutoTokenizer\u001b[38;5;241m.\u001b[39mfrom_pretrained(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msaved_mental_status_bert\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      5\u001b[0m encode \u001b[38;5;241m=\u001b[39m pickle\u001b[38;5;241m.\u001b[39mload(\u001b[38;5;28mopen\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mencode.pkl\u001b[39m\u001b[38;5;124m\"\u001b[39m , \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrb\u001b[39m\u001b[38;5;124m\"\u001b[39m))\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/transformers/models/auto/auto_factory.py:526\u001b[0m, in \u001b[0;36m_BaseAutoModelClass.from_pretrained\u001b[0;34m(cls, pretrained_model_name_or_path, *model_args, **kwargs)\u001b[0m\n\u001b[1;32m    523\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m kwargs\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mquantization_config\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    524\u001b[0m     _ \u001b[38;5;241m=\u001b[39m kwargs\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mquantization_config\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m--> 526\u001b[0m config, kwargs \u001b[38;5;241m=\u001b[39m \u001b[43mAutoConfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_pretrained\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    527\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpretrained_model_name_or_path\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    528\u001b[0m \u001b[43m    \u001b[49m\u001b[43mreturn_unused_kwargs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    529\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtrust_remote_code\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtrust_remote_code\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    530\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcode_revision\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcode_revision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    531\u001b[0m \u001b[43m    \u001b[49m\u001b[43m_commit_hash\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcommit_hash\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    532\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mhub_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    533\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    534\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    536\u001b[0m \u001b[38;5;66;03m# if torch_dtype=auto was passed here, ensure to pass it on\u001b[39;00m\n\u001b[1;32m    537\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m kwargs_orig\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtorch_dtype\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mauto\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/transformers/models/auto/configuration_auto.py:1006\u001b[0m, in \u001b[0;36mAutoConfig.from_pretrained\u001b[0;34m(cls, pretrained_model_name_or_path, **kwargs)\u001b[0m\n\u001b[1;32m   1003\u001b[0m trust_remote_code \u001b[38;5;241m=\u001b[39m kwargs\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtrust_remote_code\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m   1004\u001b[0m code_revision \u001b[38;5;241m=\u001b[39m kwargs\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcode_revision\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[0;32m-> 1006\u001b[0m config_dict, unused_kwargs \u001b[38;5;241m=\u001b[39m \u001b[43mPretrainedConfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_config_dict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpretrained_model_name_or_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1007\u001b[0m has_remote_code \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mauto_map\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m config_dict \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAutoConfig\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m config_dict[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mauto_map\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m   1008\u001b[0m has_local_code \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel_type\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m config_dict \u001b[38;5;129;01mand\u001b[39;00m config_dict[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel_type\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;129;01min\u001b[39;00m CONFIG_MAPPING\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/transformers/configuration_utils.py:567\u001b[0m, in \u001b[0;36mPretrainedConfig.get_config_dict\u001b[0;34m(cls, pretrained_model_name_or_path, **kwargs)\u001b[0m\n\u001b[1;32m    565\u001b[0m original_kwargs \u001b[38;5;241m=\u001b[39m copy\u001b[38;5;241m.\u001b[39mdeepcopy(kwargs)\n\u001b[1;32m    566\u001b[0m \u001b[38;5;66;03m# Get config dict associated with the base config file\u001b[39;00m\n\u001b[0;32m--> 567\u001b[0m config_dict, kwargs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_config_dict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpretrained_model_name_or_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    568\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m config_dict \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    569\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m {}, kwargs\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/transformers/configuration_utils.py:626\u001b[0m, in \u001b[0;36mPretrainedConfig._get_config_dict\u001b[0;34m(cls, pretrained_model_name_or_path, **kwargs)\u001b[0m\n\u001b[1;32m    622\u001b[0m configuration_file \u001b[38;5;241m=\u001b[39m kwargs\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_configuration_file\u001b[39m\u001b[38;5;124m\"\u001b[39m, CONFIG_NAME) \u001b[38;5;28;01mif\u001b[39;00m gguf_file \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m gguf_file\n\u001b[1;32m    624\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    625\u001b[0m     \u001b[38;5;66;03m# Load from local folder or from cache or download from model Hub and cache\u001b[39;00m\n\u001b[0;32m--> 626\u001b[0m     resolved_config_file \u001b[38;5;241m=\u001b[39m \u001b[43mcached_file\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    627\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpretrained_model_name_or_path\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    628\u001b[0m \u001b[43m        \u001b[49m\u001b[43mconfiguration_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    629\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcache_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    630\u001b[0m \u001b[43m        \u001b[49m\u001b[43mforce_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    631\u001b[0m \u001b[43m        \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    632\u001b[0m \u001b[43m        \u001b[49m\u001b[43mresume_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresume_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    633\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlocal_files_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlocal_files_only\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    634\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtoken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    635\u001b[0m \u001b[43m        \u001b[49m\u001b[43muser_agent\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muser_agent\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    636\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrevision\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    637\u001b[0m \u001b[43m        \u001b[49m\u001b[43msubfolder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msubfolder\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    638\u001b[0m \u001b[43m        \u001b[49m\u001b[43m_commit_hash\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcommit_hash\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    639\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    640\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m resolved_config_file \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    641\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m, kwargs\n", "File \u001b[0;32m/opt/conda/lib/python3.10/site-packages/transformers/utils/hub.py:446\u001b[0m, in \u001b[0;36mcached_file\u001b[0;34m(path_or_repo_id, filename, cache_dir, force_download, resume_download, proxies, token, revision, local_files_only, subfolder, repo_type, user_agent, _raise_exceptions_for_gated_repo, _raise_exceptions_for_missing_entries, _raise_exceptions_for_connection_errors, _commit_hash, **deprecated_kwargs)\u001b[0m\n\u001b[1;32m    440\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m (\n\u001b[1;32m    441\u001b[0m         resolved_file \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    442\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m _raise_exceptions_for_missing_entries\n\u001b[1;32m    443\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m _raise_exceptions_for_connection_errors\n\u001b[1;32m    444\u001b[0m     ):\n\u001b[1;32m    445\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m resolved_file\n\u001b[0;32m--> 446\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mEnvironmentError\u001b[39;00m(\n\u001b[1;32m    447\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWe couldn\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt connect to \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mHUGGINGFACE_CO_RESOLVE_ENDPOINT\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m to load this file, couldn\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt find it in the\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    448\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m cached files and it looks like \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath_or_repo_id\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m is not the path to a directory containing a file named\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    449\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfull_filename\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mCheckout your internet connection or see how to run the library in offline mode at\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    450\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhttps://huggingface.co/docs/transformers/installation#offline-mode\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    451\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n\u001b[1;32m    452\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m EntryNotFoundError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    453\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m _raise_exceptions_for_missing_entries:\n", "\u001b[0;31mOSError\u001b[0m: We couldn't connect to 'https://huggingface.co' to load this file, couldn't find it in the cached files and it looks like saved_mental_status_bert is not the path to a directory containing a file named config.json.\nCheckout your internet connection or see how to run the library in offline mode at 'https://huggingface.co/docs/transformers/installation#offline-mode'."], "ename": "OSError", "evalue": "We couldn't connect to 'https://huggingface.co' to load this file, couldn't find it in the cached files and it looks like saved_mental_status_bert is not the path to a directory containing a file named config.json.\nCheckout your internet connection or see how to run the library in offline mode at 'https://huggingface.co/docs/transformers/installation#offline-mode'.", "output_type": "error"}], "execution_count": 10}, {"cell_type": "markdown", "source": "# Detection System", "metadata": {}}, {"cell_type": "code", "source": "def detection_text(text) :\n    cleaned_text = preprocessing(text)\n\n    inputs = tok(cleaned_text , return_tensors=\"pt\" , padding=True , truncation=True , max_length=512)\n    outputs = model(**inputs)\n    logists = outputs.logits  # Purpose: Extracts the logits from the model outputs. Logits are the raw, unnormalized scores for each class before applying softmax.\n    pred_classes = torch.argmax(logists , dim=1).item()\n    return encode.inverse_transform([pred_classes])[0]\n\n\nsample_texts = [\n    \"I feel perfectly fine today, nothing to worry about.\",\n    \"I can't stop thinking about what will happen if things go wrong.\",\n    \"Lately, I've been on a high, feeling like I can do anything!\",\n    \"I'm so sad, I just can't seem to get out of bed anymore.\",\n    \"I'm constantly thinking about how much better everyone else is doing than me.\",\n    \"I don't think I can keep going, everything feels so hopeless.\",\n    \"I had a really good day, spent some time with my friends.\",\n    \"I'm overwhelmed by the idea that I might lose everything.\",\n    \"I feel like nothing matters anymore, I just want to give up.\",\n    \"I'm okay today, but sometimes I get really anxious for no reason.\"\n]\n\n\nfor t in sample_texts :\n    pred_classes = detection_text(t)\n    print(f\"Sentence: {t}\\nPredicted class: ( {pred_classes} )\\n\")\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2024-12-02T01:59:33.959171Z", "iopub.execute_input": "2024-12-02T01:59:33.960031Z", "iopub.status.idle": "2024-12-02T01:59:34.51725Z", "shell.execute_reply.started": "2024-12-02T01:59:33.959992Z", "shell.execute_reply": "2024-12-02T01:59:34.516283Z"}}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": "# End ", "metadata": {}}]}
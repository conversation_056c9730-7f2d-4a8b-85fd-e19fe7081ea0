import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Audio Assets Tests', () {
    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    test('Audio files should be accessible', () async {
      // Test if audio files can be loaded from assets
      final audioFiles = [
        'audio/audio-1.wav',
        'audio/audio-2.wav',
        'audio/audio-3.mp3',
        'audio/ocean-waves.mp3',
      ];

      for (String audioFile in audioFiles) {
        try {
          final data = await rootBundle.load(audioFile);
          expect(data.lengthInBytes, greaterThan(0),
              reason: 'Audio file $audioFile should not be empty');
          print(
              '✅ $audioFile loaded successfully (${data.lengthInBytes} bytes)');
        } catch (e) {
          fail('❌ Failed to load audio file $audioFile: $e');
        }
      }
    });

    test('Audio file paths should match sound configurations', () {
      final soundConfigs = {
        'Rain Sounds': 'audio/audio-1.wav',
        'Ocean Waves': 'audio/ocean-waves.mp3',
        'Forest Sounds': 'audio/audio-2.wav',
        'White Noise': 'audio/audio-3.mp3',
        'Fireplace': 'audio/audio-1.wav',
        'Piano Music': 'audio/audio-2.wav',
      };

      // Verify that all configured audio paths exist
      soundConfigs.forEach((soundName, assetPath) {
        expect(assetPath, isNotEmpty,
            reason: 'Sound $soundName should have a valid asset path');
        expect(assetPath, startsWith('audio/'),
            reason: 'Asset path should start with audio/');
      });
    });
  });
}
